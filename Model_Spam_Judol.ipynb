# 1. Install Libraries (Jika di Google Colab)
!pip install transformers datasets scikit-learn

import pandas as pd
from sklearn.model_selection import train_test_split
from datasets import Dataset
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
import torch

# 2. Load & Preprocess Data
# Misal file CSV dengan kolom 'komentar' dan 'label' (0=tidak judol, 1=judol)
df = pd.read_csv('/content/Dataset.csv')

print(df.columns)

# 2. Pastikan semua nama kolom lower case agar konsisten
df.columns = df.columns.str.lower()

# 3. Cek nama kolom label (asumsi: 'label' dalam huruf kecil)
if 'label' not in df.columns:
    print("Nama kolom label:", df.columns)  # tampilkan semua nama kolom agar bisa dicek
    raise ValueError("Kolom 'label' tidak ditemukan!")

# 4. <PERSON>uang baris dengan label kosong (NaN)
initial_count = len(df)
df = df.dropna(subset=['label'])
after_count = len(df)
print(f"Data sebelum: {initial_count}, setelah buang label kosong: {after_count}")

# 5. Split data (stratify harus label tanpa NaN)
train_df, val_df = train_test_split(
    df,
    test_size=0.2,
    stratify=df['label'],
    random_state=42
)

print("Jumlah train data:", len(train_df))
print("Jumlah validasi data:", len(val_df))

# This cell will be handled later in the fixed dataset preparation

# This cell will be handled later in the fixed dataset preparation

# 3. Load Tokenizer IndoBERT
tokenizer = BertTokenizer.from_pretrained('indobenchmark/indobert-base-p1')

# Tokenization will be handled in the fixed dataset preparation cell

# 4. Load Model IndoBERT for Classification
model = BertForSequenceClassification.from_pretrained(
    'indobenchmark/indobert-base-p1',
    num_labels=2  # binary classification
)

# 5. Training Arguments & Metric
from transformers import TrainingArguments
training_args = TrainingArguments(
    output_dir='./results',
    eval_strategy='epoch',
    save_strategy='epoch',
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=32,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=20,
    save_total_limit=1,
    load_best_model_at_end=True,
    metric_for_best_model='accuracy'
)


from sklearn.metrics import accuracy_score, f1_score

def compute_metrics(eval_pred):
    logits, labels = eval_pred
    preds = logits.argmax(axis=-1)
    acc = accuracy_score(labels, preds)
    f1 = f1_score(labels, preds)
    return {'accuracy': acc, 'f1': f1}

# 6. Trainer - This will be created after the fixed dataset preparation

# Fix the dataset preparation
# Ensure labels are integers
df['label'] = df['label'].astype(int)

# Recreate train/val split with proper labels
train_df, val_df = train_test_split(df, test_size=0.2, stratify=df['label'], random_state=42)

# Rename label column to 'labels' for HuggingFace compatibility
train_df = train_df.rename(columns={'label': 'labels'})
val_df = val_df.rename(columns={'label': 'labels'})

# Create datasets
train_dataset = Dataset.from_pandas(train_df)
val_dataset = Dataset.from_pandas(val_df)

# Apply tokenization
def preprocess_func(examples):
    return tokenizer(
        examples['comment'],
        padding='max_length',
        truncation=True,
        max_length=128
    )

train_dataset = train_dataset.map(preprocess_func, batched=True)
val_dataset = val_dataset.map(preprocess_func, batched=True)

# Set format for PyTorch
train_dataset.set_format('torch', columns=['input_ids', 'attention_mask', 'labels'])
val_dataset.set_format('torch', columns=['input_ids', 'attention_mask', 'labels'])

# 6. Create Trainer with Fixed Datasets
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics
)

# 7. Training
trainer.train()

# 8. Save Trained Model
trainer.save_model('./indobert-judol-classifier')
tokenizer.save_pretrained('./indobert-judol-classifier')